// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Loading Screen Management
    const loadingScreen = document.getElementById('loading-screen');

    // Hide loading screen after page loads
    window.addEventListener('load', function() {
        setTimeout(() => {
            loadingScreen.classList.add('fade-out');

            // Remove loading screen from DOM after animation
            setTimeout(() => {
                loadingScreen.remove();
            }, 500);
        }, 1000); // Show loading for at least 1 second
    });
    // Custom Cursor - Improved
    const cursor = document.querySelector('.cursor');
    const cursorFollower = document.querySelector('.cursor-follower');

    let mouseX = window.innerWidth / 2;
    let mouseY = window.innerHeight / 2;
    let followerX = mouseX;
    let followerY = mouseY;

    // Initialize cursor positions
    cursor.style.transform = `translate(${mouseX}px, ${mouseY}px)`;
    cursorFollower.style.transform = `translate(${followerX}px, ${followerY}px)`;

    // Mouse movement with immediate cursor update
    document.addEventListener('mousemove', function(e) {
        mouseX = e.clientX;
        mouseY = e.clientY;

        // Update cursor position immediately for responsiveness
        cursor.style.transform = `translate(${mouseX}px, ${mouseY}px)`;
    });

    // Smooth follower animation with precise tracking
    function animateFollower() {
        const ease = 0.18; // Balanced for smooth following

        // Always animate for smooth following
        followerX += (mouseX - followerX) * ease;
        followerY += (mouseY - followerY) * ease;

        // Use transform for better performance and precision
        cursorFollower.style.transform = `translate(${followerX}px, ${followerY}px)`;

        requestAnimationFrame(animateFollower);
    }
    animateFollower();

    // Improved hover effects with precise timing
    const interactiveElements = document.querySelectorAll('a, button, .glass-card, .nav-link, .btn');

    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            cursor.classList.add('hover');
            cursorFollower.style.width = '12px';
            cursorFollower.style.height = '12px';
            cursorFollower.style.opacity = '1';
        });

        element.addEventListener('mouseleave', function() {
            cursor.classList.remove('hover');
            cursorFollower.style.width = '6px';
            cursorFollower.style.height = '6px';
            cursorFollower.style.opacity = '0.9';
        });
    });

    // Smooth cursor visibility
    document.addEventListener('mouseleave', function() {
        cursor.style.opacity = '0';
        cursorFollower.style.opacity = '0';
    });

    document.addEventListener('mouseenter', function() {
        cursor.style.opacity = '1';
        cursorFollower.style.opacity = '1';
    });
    // Mobile Navigation Toggle
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');

    hamburger.addEventListener('click', function() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Navbar background on scroll
    const navbar = document.querySelector('.navbar');
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(25, 25, 40, 0.95)';
            navbar.style.borderBottomColor = 'rgba(255, 255, 255, 0.1)';
        } else {
            navbar.style.background = 'rgba(25, 25, 40, 0.8)';
            navbar.style.borderBottomColor = 'rgba(255, 255, 255, 0.05)';
        }
    });

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all glass cards for animation
    document.querySelectorAll('.glass-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Parallax effect for hero section
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const heroBackground = document.querySelector('.hero-background');
        if (heroBackground) {
            heroBackground.style.transform = `translateY(${scrolled * 0.3}px)`;
        }
    });

    // Typing animation for hero title
    function typeWriter(element, text, speed = 100) {
        let i = 0;
        element.innerHTML = '';

        function type() {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }
        type();
    }

    // Initialize typing animation after a delay
    setTimeout(() => {
        const studioName = document.querySelector('.studio-name');
        if (studioName) {
            typeWriter(studioName, 'Qyi Games Studio', 150);
        }
    }, 1000);

    // Animated counters for stats
    function animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);

        function updateCounter() {
            start += increment;
            if (start < target) {
                element.textContent = Math.floor(start) + '+';
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target + '+';
            }
        }
        updateCounter();
    }

    // Observe stats for counter animation
    const statsObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const statNumber = entry.target.querySelector('.stat-number');
                const text = statNumber.textContent;
                const number = parseInt(text.replace(/\D/g, ''));

                if (number && !statNumber.classList.contains('animated')) {
                    statNumber.classList.add('animated');
                    animateCounter(statNumber, number);
                }
            }
        });
    }, { threshold: 0.5 });

    document.querySelectorAll('.stat-item').forEach(stat => {
        statsObserver.observe(stat);
    });

    // Project card hover effects
    document.querySelectorAll('.project-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Technology card animations
    document.querySelectorAll('.tech-card').forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;

        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) rotateY(5deg)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) rotateY(0)';
        });
    });

    // Social icons hover effects
    document.querySelectorAll('.social-icon').forEach(icon => {
        icon.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) rotate(360deg)';
        });

        icon.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) rotate(0deg)';
        });
    });

    // Particle effect for hero section
    function createParticles() {
        const hero = document.querySelector('.hero');
        const particleCount = 50;

        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: absolute;
                width: 2px;
                height: 2px;
                background: rgba(255, 255, 255, 0.5);
                border-radius: 50%;
                pointer-events: none;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: float ${3 + Math.random() * 4}s ease-in-out infinite;
                animation-delay: ${Math.random() * 2}s;
            `;
            hero.appendChild(particle);
        }
    }

    // Add particle animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }
    `;
    document.head.appendChild(style);

    // Initialize particles
    createParticles();

    // Add subtle sparkle effect on click (only on interactive elements)
    const clickableElements = document.querySelectorAll('a, button, .btn, .nav-link');

    clickableElements.forEach(element => {
        element.addEventListener('click', function(e) {
            createSparkle(e.clientX, e.clientY);
        });
    });

    function createSparkle(x, y) {
        const sparkle = document.createElement('div');
        sparkle.className = 'sparkle';
        sparkle.style.cssText = `
            position: fixed;
            left: ${x}px;
            top: ${y}px;
            width: 4px;
            height: 4px;
            background: #a8a8ff;
            border-radius: 50%;
            pointer-events: none;
            z-index: 10000;
            animation: sparkle-animation 0.4s ease-out forwards;
        `;
        document.body.appendChild(sparkle);

        setTimeout(() => {
            sparkle.remove();
        }, 400);
    }

    // Add sparkle animation CSS
    const sparkleStyle = document.createElement('style');
    sparkleStyle.textContent = `
        @keyframes sparkle-animation {
            0% {
                transform: translate(-50%, -50%) scale(0);
                opacity: 0.8;
            }
            30% {
                transform: translate(-50%, -50%) scale(1.2);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(0);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(sparkleStyle);

    // Enhanced glass card interactions - Throttled
    document.querySelectorAll('.glass-card').forEach(card => {
        let cardTimeout;

        card.addEventListener('mouseenter', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            this.style.setProperty('--mouse-x', x + 'px');
            this.style.setProperty('--mouse-y', y + 'px');
        });

        card.addEventListener('mousemove', function(e) {
            // Throttle mousemove for better performance
            if (cardTimeout) return;

            cardTimeout = setTimeout(() => {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                this.style.setProperty('--mouse-x', x + 'px');
                this.style.setProperty('--mouse-y', y + 'px');

                cardTimeout = null;
            }, 16); // ~60fps
        });

        card.addEventListener('mouseleave', function() {
            // Reset to center when mouse leaves
            this.style.setProperty('--mouse-x', '50%');
            this.style.setProperty('--mouse-y', '50%');
        });
    });

    // Video background management
    const video = document.querySelector('.hero-background video');
    const videoFallback = document.querySelector('.video-fallback');

    if (video) {
        // Handle video loading errors
        video.addEventListener('error', function() {
            console.log('Video failed to load, showing fallback background');
            video.style.display = 'none';
            if (videoFallback) {
                videoFallback.style.display = 'block';
            }
        });

        // Ensure video plays
        video.addEventListener('loadeddata', function() {
            video.play().catch(function(error) {
                console.log('Video autoplay failed:', error);
                video.style.display = 'none';
                if (videoFallback) {
                    videoFallback.style.display = 'block';
                }
            });
        });

        // Fallback if video doesn't load within 5 seconds
        setTimeout(function() {
            if (video.readyState < 2) {
                console.log('Video loading timeout, showing fallback');
                video.style.display = 'none';
                if (videoFallback) {
                    videoFallback.style.display = 'block';
                }
            }
        }, 5000);
    }

    // Loading animation
    window.addEventListener('load', function() {
        document.body.style.opacity = '0';
        document.body.style.transition = 'opacity 0.5s ease';

        setTimeout(() => {
            document.body.style.opacity = '1';
        }, 100);
    });

    // Scroll progress indicator
    function updateScrollProgress() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.offsetHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;

        let progressBar = document.querySelector('.scroll-progress');
        if (!progressBar) {
            progressBar = document.createElement('div');
            progressBar.className = 'scroll-progress';
            progressBar.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 0%;
                height: 3px;
                background: linear-gradient(45deg,rgb(117, 107, 255),rgb(0, 26, 255));
                z-index: 9999;
                transition: width 0.1s ease;
            `;
            document.body.appendChild(progressBar);
        }

        progressBar.style.width = scrollPercent + '%';
    }

    window.addEventListener('scroll', updateScrollProgress);

    // Easter egg: Konami code
    let konamiCode = [];
    const konamiSequence = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];

    document.addEventListener('keydown', function(e) {
        konamiCode.push(e.code);
        if (konamiCode.length > konamiSequence.length) {
            konamiCode.shift();
        }

        if (konamiCode.join(',') === konamiSequence.join(',')) {
            // Easter egg activated
            document.body.style.filter = 'hue-rotate(180deg)';
            setTimeout(() => {
                document.body.style.filter = 'none';
            }, 3000);

            // Show message
            const message = document.createElement('div');
            message.textContent = '🎮 Developer Mode Activated! 🎮';
            message.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.9);
                color: #4ecdc4;
                padding: 20px;
                border-radius: 10px;
                z-index: 10000;
                font-size: 1.2rem;
                font-weight: bold;
            `;
            document.body.appendChild(message);

            setTimeout(() => {
                message.remove();
            }, 3000);

            konamiCode = [];
        }
    });

    // Performance optimization: Throttle scroll events
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    // Apply throttling to scroll events
    window.addEventListener('scroll', throttle(function() {
        updateScrollProgress();
    }, 16)); // ~60fps

    // Team card animations
    document.querySelectorAll('.team-card').forEach((card, index) => {
        card.style.animationDelay = `${index * 0.2}s`;

        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';

            // Add subtle rotation effect
            const teamImage = this.querySelector('.team-image');
            if (teamImage) {
                teamImage.style.transform = 'rotate(2deg)';
            }
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';

            const teamImage = this.querySelector('.team-image');
            if (teamImage) {
                teamImage.style.transform = 'rotate(0deg)';
            }
        });
    });

    // Enhanced project card interactions
    document.querySelectorAll('.project-card').forEach(card => {
        const gameplatform = card.querySelector('.game-platform');

        card.addEventListener('mouseenter', function() {
            if (gameplatform) {
                gameplatform.style.transform = 'scale(1.1) rotate(5deg)';
            }
        });

        card.addEventListener('mouseleave', function() {
            if (gameplatform) {
                gameplatform.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });

    // Skill tags hover effects
    document.querySelectorAll('.skill-tag').forEach(tag => {
        tag.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.05)';
            this.style.boxShadow = '0 5px 15px rgba(168, 168, 255, 0.3)';
        });

        tag.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = 'none';
        });
    });

    // Add loading animation for team images
    document.querySelectorAll('.team-image img').forEach(img => {
        img.addEventListener('load', function() {
            this.style.opacity = '0';
            this.style.transform = 'scale(0.8)';

            setTimeout(() => {
                this.style.transition = 'all 0.6s ease';
                this.style.opacity = '1';
                this.style.transform = 'scale(1)';
            }, 100);
        });
    });

    // Add welcome message after loading
    setTimeout(() => {
        console.log('🎮 Qyi Games Studio website loaded successfully!');
        console.log('Made with ❤️ for the gaming community');
        console.log('🌟 Features: Team Section, Enhanced Animations, Loading Screen');
        console.log('🎨 Design: Glassmorphism, Arabic Support, Rubik Font');
        console.log('⚡ Performance: Optimized animations and responsive design');
    }, 1500);

    // Add subtle page transition effect
    document.body.style.opacity = '0';
    setTimeout(() => {
        document.body.style.transition = 'opacity 0.5s ease';
        document.body.style.opacity = '1';
    }, 100);
});
