/* Import Rubik Font */
@import url('https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300..900;1,300..900&display=swap');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: "Rubik", sans-serif;
    line-height: 1.8;
    color: #ffffff;
    background: linear-gradient(135deg, #191928 0%, #18191f 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
    cursor: none;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #191928 0%, #18191f 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.fade-out {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    animation: fadeInUp 1s ease;
}

.loading-logo h2 {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #a8a8ff, #c8c8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 30px rgba(168, 168, 255, 0.5);
}

.loading-logo .english-text {
    font-size: 1.2rem;
    opacity: 0.7;
    margin-bottom: 2rem;
}

.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.spinner-ring {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(168, 168, 255, 0.3);
    border-top: 2px solid #a8a8ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
    animation-delay: 0.2s;
}

.spinner-ring:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1rem;
    opacity: 0.8;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* Arabic text styling */
html[dir="rtl"] {
    text-align: right;
}

html[dir="rtl"] .nav-menu {
    flex-direction: row-reverse;
}

html[dir="rtl"] .hero-buttons {
    flex-direction: row-reverse;
}

html[dir="rtl"] .contact-item {
    flex-direction: row-reverse;
    text-align: right;
}

html[dir="rtl"] .project-links {
    flex-direction: row-reverse;
}

.english-text {
    font-family: "Rubik", sans-serif;
    font-size: 1.2rem;
    opacity: 0.7;
    font-weight: 300;
}

/* Arabic text with Rubik support */
.arabic-text {
    font-family: "Rubik", sans-serif;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(200, 200, 255, 0.08) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Custom Cursor - Improved */
.cursor {
    width: 20px;
    height: 20px;
    border: 2px solid #a8a8ff;
    border-radius: 50%;
    position: fixed;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translate(-50%, -50%);
    mix-blend-mode: difference;
    will-change: transform;
}

.cursor-follower {
    width: 6px;
    height: 6px;
    background: #a8a8ff;
    border-radius: 50%;
    position: fixed;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 9998;
    transition: width 0.3s ease, height 0.3s ease;
    transform: translate(-50%, -50%);
    opacity: 0.9;
    will-change: transform;
}

.cursor.hover {
    width: 32px;
    height: 32px;
    background: rgba(168, 168, 255, 0.08);
    border-color: #c8c8ff;
    border-width: 1px;
}

/* Floating Elements */
.floating-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-crystal {
    position: absolute;
    width: 16px;
    height: 16px;
    background: linear-gradient(45deg, rgba(168, 168, 255, 0.2), rgba(200, 200, 255, 0.08));
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation: float 12s ease-in-out infinite;
}

.crystal-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.crystal-2 {
    top: 60%;
    right: 15%;
    animation-delay: 4s;
    transform: rotate(180deg);
}

.crystal-3 {
    top: 80%;
    left: 20%;
    animation-delay: 8s;
    transform: rotate(90deg);
}

.floating-orb {
    position: absolute;
    width: 12px;
    height: 12px;
    background: radial-gradient(circle, rgba(168, 168, 255, 0.25), transparent);
    border-radius: 50%;
    animation: orb-float 15s ease-in-out infinite;
}

.orb-1 {
    top: 30%;
    right: 20%;
    animation-delay: 2s;
}

.orb-2 {
    top: 70%;
    left: 80%;
    animation-delay: 7s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.4;
    }
    50% {
        transform: translateY(-15px) rotate(90deg);
        opacity: 0.7;
    }
}

@keyframes orb-float {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-20px) scale(1.1);
        opacity: 0.6;
    }
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Glassmorphism Card */
.glass-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(25px);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 0 1px rgba(168, 168, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.glass-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(168, 168, 255, 0.1) 0%,
        transparent 50%,
        rgba(200, 200, 255, 0.05) 100%);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transition: left 0.6s ease;
}

.glass-card:hover::before {
    left: 100%;
}

.glass-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 0 1px rgba(168, 168, 255, 0.3),
        0 0 30px rgba(168, 168, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

.glass-card:hover::after {
    opacity: 1;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    background: rgba(25, 25, 40, 0.8);
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    font-weight: 700;
    font-size: 1.5rem;
    background: linear-gradient(45deg, #051190, #051190);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #a8a8ff;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #051190, #051190);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #ffffff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-background video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
}

.video-fallback {
    width: 100%;
    height: 100%;
    background:
        linear-gradient(135deg, #191928 0%, #18191f 100%),
        radial-gradient(circle at 20% 80%, rgba(168, 168, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(200, 200, 255, 0.08) 0%, transparent 50%);
    position: absolute;
    top: 0;
    left: 0;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% {
        background-position: 0% 50%, 20% 80%, 80% 20%;
    }
    50% {
        background-position: 100% 50%, 80% 20%, 20% 80%;
    }
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(135deg, rgba(25, 25, 40, 0.7) 0%, rgba(24, 25, 31, 0.8) 100%),
        radial-gradient(circle at center, rgba(200, 200, 255, 0.1) 0%, transparent 70%);
    z-index: -1;
}

.hero-content {
    text-align: center;
    z-index: 1;
}

.hero-card {
    padding: 3rem;
    max-width: 800px;
    margin: 0 auto;
    animation: fadeInUp 1s ease;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.studio-name {
    background: linear-gradient(45deg, #051190, #051190);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.arabic-text {
    font-size: 2rem;
    opacity: 0.8;
    font-weight: 400;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 14px 32px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #030841, #0015ff);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(82, 82, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(168, 168, 255, 0.4);
    background: linear-gradient(135deg, #0015ff, #0015ff);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.08);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border-right: 2px solid #ffffff;
    border-bottom: 2px solid #ffffff;
    transform: rotate(45deg);
}

/* Sections */
section {
    padding: 100px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #051190, #051190);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.2rem;
    opacity: 0.8;
}

/* About Section */
.about-card {
    padding: 3rem;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
}

.about-text p {
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.8;
}

.about-stats {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #ffffff #ffffff);
    -webkit-background-clip: text;
    background-clip: text;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 0.5rem;
}

/* Projects Section */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.project-card {
    overflow: hidden;
    transition: all 0.3s ease;
}

.project-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-links {
    display: flex;
    gap: 1rem;
}

.project-content {
    padding: 2rem;
}

.project-content h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
}

.project-content p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.project-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Technologies Section */
.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.tech-card {
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.tech-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffffff, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tech-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #ffffff;
}

.tech-card p {
    opacity: 0.9;
}

/* Contact Section */
.contact-card {
    padding: 3rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.contact-info h3,
.social-links h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #ffffff;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.contact-item i {
    color: #ffffff;
    width: 20px;
}

.social-icons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.social-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-icon:hover {
    background: linear-gradient(45deg, #000000, #0000ff);
    transform: translateY(-3px);
}

/* Footer */
.footer {
    background: rgba(25, 25, 40, 0.6);
    backdrop-filter: blur(20px);
    padding: 3rem 0;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

.footer-content p {
    margin-bottom: 0.5rem;
    opacity: 0.8;
    font-size: 0.95rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: rgba(0, 0, 0, 0.9);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .arabic-text {
        font-size: 1.5rem;
    }

    .hero-card {
        padding: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .about-card {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-card {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .tech-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

/* Additional Enhancements */

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(25, 25, 40, 0.5);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #000000, #000000);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #000000, #000000);
}

/* Selection styling */
::selection {
    background: rgba(168, 168, 255, 0.3);
    color: #ffffff;
}

::-moz-selection {
    background: rgba(168, 168, 255, 0.3);
    color: #ffffff;
}

/* Enhanced focus states */
.nav-link:focus,
.btn:focus,
.social-icon:focus {
    outline: 2px solid rgba(168, 168, 255, 0.5);
    outline-offset: 2px;
}

/* Loading animation for images */
.project-image img {
    opacity: 0;
    animation: fadeIn 0.6s ease forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

/* Improved glass card content spacing */
.glass-card > * {
    position: relative;
    z-index: 1;
}

/* Enhanced hover states for project cards */
.project-card {
    cursor: pointer;
}

.project-card:hover .project-content h3 {
    color: #c8c8ff;
    transition: color 0.3s ease;
}

/* Improved tag styling */
.tag {
    background: rgba(168, 168, 255, 0.1);
    border: 1px solid rgba(168, 168, 255, 0.3);
    color: #a8a8ff;
    transition: all 0.3s ease;
}

.tag:hover {
    background: rgba(168, 168, 255, 0.2);
    transform: scale(1.05);
}

/* Enhanced social icons */
.social-icon {
    position: relative;
    overflow: hidden;
}

.social-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.social-icon:hover::before {
    width: 100%;
    height: 100%;
}

/* Improved section spacing */
section:not(.hero) {
    position: relative;
}

section:not(.hero)::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 100px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(168, 168, 255, 0.3), transparent);
    transform: translateX(-50%);
}

/* Enhanced glass card mouse tracking */
.glass-card {
    --mouse-x: 50%;
    --mouse-y: 50%;
}

.glass-card:hover::after {
    background: radial-gradient(circle at var(--mouse-x) var(--mouse-y),
        rgba(168, 168, 255, 0.2) 0%,
        transparent 50%);
}

/* Magical glow effects */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent);
    transition: left 0.5s ease;
}

.btn:hover::after {
    left: 100%;
}

/* Crystal shine effect - Subtle */
.floating-crystal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        transparent 40%,
        rgba(255, 255, 255, 0.15) 50%,
        transparent 60%);
    animation: crystal-shine 8s ease-in-out infinite;
}

@keyframes crystal-shine {
    0%, 100% { opacity: 0; }
    50% { opacity: 0.6; }
}

/* Pulsing orbs - Gentle */
.floating-orb::after {
    content: '';
    position: absolute;
    top: -30%;
    left: -30%;
    width: 160%;
    height: 160%;
    background: radial-gradient(circle,
        rgba(168, 168, 255, 0.06) 0%,
        transparent 70%);
    animation: pulse 10s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(0.9);
        opacity: 0.2;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.4;
    }
}

/* Text glow effect */

.hero-title .studio-name {
    text-shadow: 0 0 30px rgba(168, 168, 255, 0.5);
}

/* Enhanced mobile experience */
@media (max-width: 768px) {
    .team-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .team-card {
        padding: 1.5rem;
    }

    .team-image {
        width: 150px;
        height: 150px;
    }

    .game-platform {
        top: 0.5rem;
        right: 0.5rem;
        padding: 0.3rem 0.6rem;
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .cursor,
    .cursor-follower {
        display: none;
    }

    body {
        cursor: auto;
    }

    .hero-card {
        padding: 1.5rem;
        margin: 0 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .english-text {
        font-size: 1rem;
    }

    .btn {
        padding: 12px 24px;
        font-size: 0.9rem;
    }

    .glass-card {
        margin: 0 1rem;
    }

    .floating-elements {
        display: none;
    }

    .team-image {
        width: 120px;
        height: 120px;
    }

    .team-content h3 {
        font-size: 1.5rem;
    }

    .team-description {
        font-size: 0.9rem;
    }

    .skill-tag {
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
    }
}

/* Performance optimizations */
.glass-card,
.btn,
.social-icon,
.project-card {
    will-change: transform;
}

/* Team Section */
.team {
    padding: 100px 0;
    background: linear-gradient(135deg, rgba(25, 25, 40, 0.8) 0%, rgba(24, 25, 31, 0.9) 100%);
    position: relative;
}

.team::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(168, 168, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(200, 200, 255, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.team-card {
    padding: 2rem;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.team-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow:
        0 25px 70px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 0 1px rgba(168, 168, 255, 0.4),
        0 0 40px rgba(168, 168, 255, 0.3);
}

.team-image {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 0 auto 2rem;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid rgba(168, 168, 255, 0.3);
    transition: all 0.4s ease;
}

.team-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
}

.team-card:hover .team-image {
    border-color: rgba(168, 168, 255, 0.6);
    box-shadow: 0 0 30px rgba(168, 168, 255, 0.4);
}

.team-card:hover .team-image img {
    transform: scale(1.1);
}

.team-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(25, 25, 40, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 50%;
}

.team-image:hover .team-overlay {
    opacity: 1;
}

.team-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(168, 168, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: rgba(168, 168, 255, 0.4);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(168, 168, 255, 0.3);
}

.team-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #a8a8ff, #c8c8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.team-role {
    font-size: 1.1rem;
    color: #a8a8ff;
    font-weight: 600;
    margin-bottom: 1rem;
}

.team-description {
    font-size: 0.95rem;
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 1.5rem;
}

.team-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.skill-tag {
    background: rgba(168, 168, 255, 0.15);
    color: #c8c8ff;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(168, 168, 255, 0.2);
    transition: all 0.3s ease;
}

.skill-tag:hover {
    background: rgba(168, 168, 255, 0.25);
    border-color: rgba(168, 168, 255, 0.4);
    transform: translateY(-2px);
}

/* Game Platform Badges */
.game-platform {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(168, 168, 255, 0.9);
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    z-index: 10;
}

.game-platform:hover {
    background: rgba(168, 168, 255, 1);
    transform: scale(1.05);
    box-shadow: 0 5px 20px rgba(168, 168, 255, 0.4);
}

.game-platform i {
    font-size: 1rem;
    animation: pulse-icon 2s ease-in-out infinite;
}

@keyframes pulse-icon {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Enhanced project cards */
.project-card {
    position: relative;
    overflow: hidden;
}

.project-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(168, 168, 255, 0.1) 50%,
        transparent 70%);
    transform: rotate(-45deg);
    transition: all 0.6s ease;
    opacity: 0;
    z-index: 1;
}

.project-card:hover::before {
    opacity: 1;
    animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(-45deg);
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(-45deg);
    }
}

/* Enhanced section transitions */
.section-header {
    position: relative;
    margin-bottom: 3rem;
}

.section-title {
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(45deg, #a8a8ff, #c8c8ff);
    border-radius: 2px;
}

/* Improved loading states */
.glass-card {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease forwards;
}

.glass-card:nth-child(1) { animation-delay: 0.1s; }
.glass-card:nth-child(2) { animation-delay: 0.2s; }
.glass-card:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    html {
        scroll-behavior: auto;
    }
}
